"use client";

import { Attachment, Message, CreateMessage, ChatRequestOptions } from "ai";
import { useState, useCallback, useRef, useEffect } from "react";

import { Message as PreviewMessage } from "@/components/custom/message";
import { useScrollToBottom } from "@/components/custom/use-scroll-to-bottom";
import { ThinkingCard } from "@/components/custom/thinking-card";

import { MultimodalInput } from "./multimodal-input";
import { Overview } from "./overview";

export function Chat({
  id,
  type,
  initialMessages,
}: {
  id: string;
  type: string;
  initialMessages: Array<Message>;
}) {
  const [messages, setMessages] = useState<Array<Message>>(initialMessages);
  const [input, setInput] = useState("");
  const [isLoading, setIsLoading] = useState(false);

  const [messagesContainerRef, messagesEndRef] =
    useScrollToBottom<HTMLDivElement>();

  const [attachments, setAttachments] = useState<Array<Attachment>>([]);

  // Thinking卡片状态
  const [showThinkingCard, setShowThinkingCard] = useState(false);
  const [thinkingContent, setThinkingContent] = useState("问题已提交，模型加载中");
  const [isThinkingLoading, setIsThinkingLoading] = useState(false);
  const [streamCompleted, setStreamCompleted] = useState(false);
  const [pendingMessages, setPendingMessages] = useState<Array<Message>>([]);

  // 用于管理打字机效果的定时器
  const typewriterTimers = useRef<Map<string, NodeJS.Timeout>>(new Map());

  // 处理待处理消息的函数
  const processPendingMessages = useCallback(() => {
    if (pendingMessages.length === 0) return;

    pendingMessages.forEach((message, index) => {
      setTimeout(() => {
        // 添加消息到主消息列表
        setMessages(prev => [...prev, { ...message, content: "" }]);

        // 开始打字机效果
        const content = message.content;
        let currentIndex = 0;

        const typewriterInterval = setInterval(() => {
          if (currentIndex < content.length) {
            // 智能分割：中文字符1个，英文单词或标点符号一组
            let charsToAdd = "";
            const char = content[currentIndex];

            if (/[\u4e00-\u9fff]/.test(char)) {
              // 中文字符，一次显示1个
              charsToAdd = char;
              currentIndex += 1;
            } else {
              // 英文字符，一次显示2-3个或到空格/标点
              let endIndex = currentIndex + 3;
              for (let i = currentIndex; i < Math.min(endIndex, content.length); i++) {
                if (/[\s.,!?;:]/.test(content[i])) {
                  endIndex = i + 1;
                  break;
                }
              }
              charsToAdd = content.slice(currentIndex, Math.min(endIndex, content.length));
              currentIndex += charsToAdd.length;
            }

            setMessages(prev => prev.map(msg =>
              msg.id === message.id
                ? { ...msg, content: msg.content + charsToAdd }
                : msg
            ));
          } else {
            clearInterval(typewriterInterval);
            typewriterTimers.current.delete(message.id);
          }
        }, 80);

        // 保存定时器引用
        typewriterTimers.current.set(message.id, typewriterInterval);
      }, index * 200); // 每个消息延迟200ms开始
    });

    // 清空待处理消息
    setPendingMessages([]);
  }, [pendingMessages]);

  // 跳过打字机效果的函数
  const skipTypewriter = useCallback((messageId: string, fullContent: string) => {
    const timer = typewriterTimers.current.get(messageId);
    if (timer) {
      clearInterval(timer);
      typewriterTimers.current.delete(messageId);
      // 立即显示完整内容
      setMessages(prev => prev.map(msg =>
        msg.id === messageId
          ? { ...msg, content: fullContent }
          : msg
      ));
    }
  }, []);

  // 组件卸载时清理所有定时器
  useEffect(() => {
    return () => {
      typewriterTimers.current.forEach((timer) => {
        clearInterval(timer);
      });
      typewriterTimers.current.clear();
    };
  }, []);

  // 自定义的提交函数，调用新的 mychat 接口
  const handleSubmit = useCallback(async (event?: { preventDefault?: () => void }) => {
    if (event?.preventDefault) {
      event.preventDefault();
    }

    if (!input.trim() || isLoading) {
      return;
    }

    const userMessage: Message = {
      id: Date.now().toString(),
      role: "user",
      content: input,
    };

    // 添加用户消息到聊天
    setMessages(prev => [...prev, userMessage]);
    const currentInput = input;

    setInput("");
    setIsLoading(true);

    // 显示Thinking卡片
    setShowThinkingCard(true);
    setThinkingContent("问题已提交，模型加载中");
    setIsThinkingLoading(true);
    setStreamCompleted(false);
    setPendingMessages([]);

    const currentMessages = [...messages, ...[{ role: "user", content: currentInput}]];
    alert(JSON.stringify(currentMessages));

    try {
      // 调用新的 mychat 接口
      const response = await fetch('/api/mychat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ id:id, type:type, currentInput: currentInput, currentMessages:currentMessages }),
      });

      if (!response.ok) {
        throw new Error('Network response was not ok');
      }

      // 创建助手消息
      // const assistantMessage: Message = {
      //   id: (Date.now() + 1).toString(),
      //   role: "assistant",
      //   content: "",
      // };

      // setMessages(prev => [...prev, assistantMessage]);

      // 处理流式响应
      const reader = response.body?.getReader();
      const decoder = new TextDecoder();

      if (reader) {
        while (true) {
          const { done, value } = await reader.read();
          if (done) {
            // 流式响应完成，隐藏Thinking卡片，开始显示消息
            setIsThinkingLoading(false);
            setStreamCompleted(true);
            setShowThinkingCard(false);

            // 开始逐个显示待处理的消息
            setTimeout(() => {
              processPendingMessages();
            }, 300);
            break;
          }

          const chunk = decoder.decode(value);
          const lines = chunk.split('\n');

          for (const line of lines) {
            if (line.startsWith('data: ')) {
              const data = line.slice(6);
              if (data === '[DONE]') {
                console.log('Stream completed');
                break;
              }

              try {
                const parsedData = JSON.parse(data);
                console.log('Received chunk:', parsedData);

                // 更新Thinking卡片内容
                if (parsedData.role === "tool") {
                  if (parsedData.name === "neo4j_search") {
                    setThinkingContent(prev => prev + "\n正在查询知识图谱");
                  } else if (parsedData.name === "chart") {
                    setThinkingContent(prev => prev + "\n正在生成图表");
                  }
                }

                // 实现流式吐字效果
                const content = parsedData.content;
                const messageId = `${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;

                // 先添加空消息到待处理队列
                const newMessage: Message = {
                  id: messageId,
                  role: parsedData.role,
                  name: parsedData.name,
                  content: content,
                };
                setPendingMessages(prev => [...prev, newMessage]);
                // 更新助手消息内容
                // setMessages(prev => prev.map(msg =>
                //   msg.id === assistantMessage.id
                //     ? { ...msg, content: msg.content + JSON.stringify(parsedData, null, 2) + '\n' }
                //     : msg
                // ));
              } catch (e) {
                console.error('Error parsing chunk:', e);
              }
            }
          }
        }
      }
    } catch (error) {
      console.error('Error calling mychat API:', error);

      // 添加错误消息
      const errorMessage: Message = {
        id: (Date.now() + 2).toString(),
        role: "assistant",
        content: "抱歉，发生了错误。请稍后再试。",
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  }, [input, isLoading]);

  const stop = useCallback(() => {
    setIsLoading(false);
  }, []);

  const append = useCallback(async (message: Message | CreateMessage, chatRequestOptions?: ChatRequestOptions) => {
    const newMessage: Message = {
      id: message.id || Date.now().toString(),
      role: message.role as "user" | "assistant" | "system" | "function" | "data" | "tool",
      content: message.content,
    };
    setMessages(prev => [...prev, newMessage]);
    return newMessage.id;
  }, []);

  return (
    <div className="flex flex-row justify-center pb-4 md:pb-8 h-dvh bg-background">
      <div className="flex flex-col justify-between items-center gap-4">
        <div
          ref={messagesContainerRef}
          className="flex flex-col gap-4 h-full w-dvw items-center overflow-y-scroll"
        >
          {messages.length === 0 && <Overview />}

          {messages.map((message) => (
            <PreviewMessage
              key={message.id}
              chatId={id}
              role={message.role}
              name={message.name || ""}
              content={message.content}
              attachments={message.experimental_attachments}
              toolInvocations={message.toolInvocations}
            />
          ))}

          {/* Thinking卡片 */}
          <ThinkingCard
            isVisible={showThinkingCard}
            content={thinkingContent}
            isLoading={isThinkingLoading}
          />

          <div
            ref={messagesEndRef}
            className="shrink-0 min-w-[24px] min-h-[24px]"
          />
        </div>

        <form className="flex flex-row gap-2 relative items-end w-full md:max-w-[500px] max-w-[calc(100dvw-32px) px-4 md:px-0">
          <MultimodalInput
            input={input}
            setInput={setInput}
            handleSubmit={handleSubmit}
            isLoading={isLoading}
            stop={stop}
            attachments={attachments}
            setAttachments={setAttachments}
            messages={messages}
            append={append}
          />
        </form>
      </div>
    </div>
  );
}

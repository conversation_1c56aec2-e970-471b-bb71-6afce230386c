import { Client } from "@langchain/langgraph-sdk";
import { auth } from "@/app/(auth)/auth";
import {
  saveChat,
} from "@/db/queries";
export async function POST(request: Request) {
  console.log('mychat API called');

  // 验证用户身份
  const session = await auth();
  if (!session) {
    return new Response("Unauthorized", { status: 401 });
  }

  try {
    // 获取请求数据
    let { id, type, currentInput, currentMessages } = await request.json();

    console.log('id:', id);
    console.log('type:', type);
    console.log('currentMessages:', currentMessages);

    // 创建 LangGraph 客户端
    const client = new Client({
      apiUrl: "http://localhost:2024"
    });
   
    if (type === "new"){
      const thread = await client.threads.create()
      id = thread.thread_id;
    }

    // 准备消息格式
    // const thread = await client.threads.create()
    const messages = [{ role: "human", content: currentInput }]; //只能用 const messages ，用其他的变量名就会导致消息没有发送，但流程会运行，造成答非所问
    const streamResponse = client.runs.stream(id, 'fe096781-5601-53d2-b2f6-0d3403f7e9ca', {
    input: { messages },
    });

    // 创建可读流
    const stream = new ReadableStream({
      async start(controller) {
        try {
          const responseMessages = [];
          for await (const chunk of streamResponse) {
            // console.log('chunk', JSON.stringify(chunk, null, 2));
            if (chunk.event === 'values') {
                if (chunk.data.messages.length) { 
                  const msg = chunk.data.messages[chunk.data.messages.length - 1]
                  if (msg.type != 'human') { 
                    console.log('msg.type', msg.type)
                    console.log('msg.content', msg.content)
                    // const message: Message = {
                    //   id: (Date.now() + 1).toString(),
                    //   role: _dic[msg.type],
                    //   content: msg.content,
                    // };
                    // 将数据编码为字符串并发送
                    const chunkData = {
                      role: msg.type == 'ai' ? 'assistant' : 'tool',
                      name: msg.type == 'ai' ? '' : msg.name,
                      content: msg.content
                    }
                    const data = JSON.stringify(chunkData);
                    responseMessages.push(chunkData);
                    const encodedData = new TextEncoder().encode(`data: ${data}\n\n`);
                    controller.enqueue(encodedData);
                  }
                }
              }
           
          }
          
          // 结束后保存聊天记录
          if (session.user && session.user.id) {
            try {
              await saveChat({
                id,
                messages: [...currentMessages, ...responseMessages],
                userId: session.user.id,
              });
            } catch (error) {
              console.error("Failed to save chat");
            }
          }
          // 发送结束标记
          const endData = new TextEncoder().encode(`data: [DONE]\n\n`);
          controller.enqueue(endData);
          controller.close();
        } catch (error) {
          console.error('Stream error:', error);
          controller.error(error);
        }
      }
    });

    // 返回流响应
    return new Response(stream, {
      headers: {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST',
        'Access-Control-Allow-Headers': 'Content-Type',
      },
    });

  } catch (error) {
    console.error('API error:', error);
    return new Response(JSON.stringify({ error: 'Internal server error' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    });
  }
}
